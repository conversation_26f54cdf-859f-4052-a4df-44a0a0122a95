"""
优化的STRM控制器
使用缓存和查询优化提升性能
"""

from typing import Dict, Any, Optional, List
from fastapi_cache.decorator import cache
from fastapi_cache.coder import JsonCoder

from app.models.system import User
from app.models.strm import MediaServer, SystemSettings, StrmTask
from app.core.query_optimizer import QueryOptimizer, CacheManager
from app.core.exceptions import HTTPException


class OptimizedStrmController:
    """优化的STRM控制器"""

    @staticmethod
    @cache(expire=300, coder=JsonCoder)  # 缓存5分钟
    async def get_media_servers_cached() -> List[Dict[str, Any]]:
        """
        获取媒体服务器列表（带缓存）
        """
        servers = await MediaServer.all().select_related("created_by").order_by("-create_time")
        return [await server.to_dict() for server in servers]

    @staticmethod
    @cache(expire=600, coder=JsonCoder)  # 缓存10分钟
    async def get_system_settings_cached() -> Dict[str, Any]:
        """
        获取系统设置（带缓存）
        """
        settings = await SystemSettings.first()
        if not settings:
            raise HTTPException(code=404, msg="系统设置未找到")
        return await settings.to_dict()

    @staticmethod
    async def get_user_tasks_optimized(
        user: User,
        page: int = 1,
        page_size: int = 10,
        search: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        获取用户任务列表（优化版本）
        """
        # 生成缓存键
        cache_key = CacheManager.get_cache_key(
            "user_tasks", user.id, page, page_size, search or "", 
            status or "", start_date or "", end_date or ""
        )
        
        # 使用缓存
        async def fetch_tasks():
            return await QueryOptimizer.get_tasks_with_relations(
                user, page, page_size, search, status, start_date, end_date
            )
        
        # 缓存时间较短，因为任务状态变化频繁
        return await CacheManager.get_or_set_cache(cache_key, fetch_tasks, expire=60)

    @staticmethod
    async def get_task_files_optimized(
        task_id: int, 
        user: User, 
        page: int = 1, 
        page_size: int = 20
    ) -> Dict[str, Any]:
        """
        获取任务文件列表（优化版本）
        """
        # 验证任务权限
        task = await StrmTask.filter(id=task_id, created_by=user).first()
        if not task:
            raise HTTPException(code=404, msg="任务不存在或无权限访问")
        
        # 生成缓存键
        cache_key = CacheManager.get_cache_key("task_files", task_id, page, page_size)
        
        async def fetch_files():
            return await QueryOptimizer.get_task_files_optimized(task_id, page, page_size)
        
        # 文件列表相对稳定，可以缓存较长时间
        return await CacheManager.get_or_set_cache(cache_key, fetch_files, expire=300)

    @staticmethod
    async def get_task_logs_optimized(
        task_id: int,
        user: User,
        page: int = 1,
        page_size: int = 50,
        level: Optional[str] = None,
        search: Optional[str] = None,
        log_type: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        获取任务日志（优化版本）
        """
        # 验证任务权限
        task = await StrmTask.filter(id=task_id, created_by=user).first()
        if not task:
            raise HTTPException(code=404, msg="任务不存在或无权限访问")
        
        # 生成缓存键
        cache_key = CacheManager.get_cache_key(
            "task_logs", task_id, page, page_size, 
            level or "", search or "", log_type or ""
        )
        
        async def fetch_logs():
            return await QueryOptimizer.get_unified_task_logs(
                task_id, page, page_size, level, search, log_type
            )
        
        # 日志查询缓存时间较短
        return await CacheManager.get_or_set_cache(cache_key, fetch_logs, expire=30)

    @staticmethod
    @cache(expire=180, coder=JsonCoder)  # 缓存3分钟
    async def get_user_task_statistics(user: User) -> Dict[str, Any]:
        """
        获取用户任务统计信息（带缓存）
        """
        return await QueryOptimizer.get_task_statistics(user)

    @staticmethod
    async def bulk_create_strm_files(files_data: List[Dict[str, Any]]) -> List:
        """
        批量创建STRM文件记录
        """
        return await QueryOptimizer.bulk_create_strm_files(files_data)

    @staticmethod
    async def bulk_update_task_status(task_ids: List[int], status: str) -> int:
        """
        批量更新任务状态
        """
        # 清除相关缓存
        from fastapi_cache import FastAPICache
        
        # 清除任务列表缓存
        await FastAPICache.clear(namespace="user_tasks")
        await FastAPICache.clear(namespace="task_stats")
        
        return await QueryOptimizer.bulk_update_task_status(task_ids, status)

    @staticmethod
    async def invalidate_task_cache(task_id: int, user_id: int):
        """
        清除任务相关缓存
        """
        from fastapi_cache import FastAPICache
        
        # 清除特定任务的缓存
        await FastAPICache.clear(namespace=f"task_files:{task_id}")
        await FastAPICache.clear(namespace=f"task_logs:{task_id}")
        
        # 清除用户任务列表缓存
        await FastAPICache.clear(namespace=f"user_tasks:{user_id}")
        await FastAPICache.clear(namespace=f"task_stats:{user_id}")

    @staticmethod
    async def get_server_status_cached(server_id: int) -> Dict[str, Any]:
        """
        获取服务器状态（带缓存）
        """
        cache_key = CacheManager.get_cache_key("server_status", server_id)
        
        async def fetch_status():
            server = await MediaServer.get_or_none(id=server_id)
            if not server:
                raise HTTPException(code=404, msg="服务器不存在")
            
            # 这里可以添加实际的服务器状态检查逻辑
            return {
                "id": server.id,
                "name": server.name,
                "status": server.status,
                "last_check": server.update_time,
            }
        
        # 服务器状态缓存1分钟
        return await CacheManager.get_or_set_cache(cache_key, fetch_status, expire=60)


class DatabaseOptimizer:
    """数据库优化工具"""

    @staticmethod
    async def analyze_query_performance():
        """
        分析查询性能（SQLite特定）
        """
        from tortoise import connections
        
        conn = connections.get("conn_system")
        
        # 获取查询计划
        queries = [
            "EXPLAIN QUERY PLAN SELECT * FROM strm_tasks WHERE created_by_id = ? ORDER BY create_time DESC",
            "EXPLAIN QUERY PLAN SELECT * FROM strm_files WHERE task_id = ? AND file_type = ?",
            "EXPLAIN QUERY PLAN SELECT * FROM strm_download_logs WHERE task_id = ? ORDER BY create_time",
        ]
        
        results = {}
        for i, query in enumerate(queries):
            try:
                result = await conn.execute_query(query, [1, "video"] if i == 1 else [1])
                results[f"query_{i+1}"] = result[1] if result[1] else []
            except Exception as e:
                results[f"query_{i+1}"] = f"Error: {str(e)}"
        
        return results

    @staticmethod
    async def optimize_database():
        """
        执行数据库优化操作
        """
        from tortoise import connections
        
        conn = connections.get("conn_system")
        
        # 执行SQLite优化命令
        optimization_commands = [
            "PRAGMA optimize;",
            "VACUUM;",
            "ANALYZE;",
        ]
        
        results = {}
        for cmd in optimization_commands:
            try:
                await conn.execute_query(cmd)
                results[cmd] = "Success"
            except Exception as e:
                results[cmd] = f"Error: {str(e)}"
        
        return results

    @staticmethod
    async def get_database_stats():
        """
        获取数据库统计信息
        """
        from tortoise import connections
        
        conn = connections.get("conn_system")
        
        # 获取表大小和行数统计
        stats_query = """
        SELECT 
            name as table_name,
            (SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND tbl_name=m.name) as index_count
        FROM sqlite_master m 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
        ORDER BY name
        """
        
        try:
            result = await conn.execute_query(stats_query)
            return result[1] if result[1] else []
        except Exception as e:
            return {"error": str(e)}


__all__ = ["OptimizedStrmController", "DatabaseOptimizer"]
