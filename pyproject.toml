[project]
name = "streamforge"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "fastapi==0.115.6",
    "uvicorn==0.34.0",
    "tortoise-orm[aiosqlite]==0.23.0",
    "pydantic==2.10.4",
    "pydantic-settings==2.7.0",
    "passlib==1.7.4",
    "pyjwt==2.10.1",
    "loguru==0.7.3",
    "aerich[toml]==0.8.1",
    "email-validator==2.2.0",
    "argon2-cffi==23.1.0",
    "fastapi-cache2==0.2.2",
    "redis==5.2.1",
    "orjson==3.10.13",
    "alibabacloud-dysmsapi20170525>=3.1.0",
    "aiofiles",
    "python-dotenv==1.0.1",
]
license ={ text = "MIT" }

[tool.ruff]
line-length = 120

[tool.ruff.lint]
exclude = ["web", "node_modules", "migrations", "src/experimental", "src/typestubs", "src/oldstuff"]
extend-select = [
    "F",
    "E",
    "W",
    "UP",
]

ignore = [
    "F403",
    "F405",
    "E501", # Line too long
    "W293",
]

[tool.ruff.format]

[tool.pyright]
include = ["app"]
exclude = ["web", "**/node_modules", "**/__pycache__", "src/experimental", "src/typestubs"]
ignore = ["migrations", "src/oldstuff"]
defineConstant = { DEBUG = false }
stubPath = "src/stubs"
reportMissingImports = true

[tool.aerich]
tortoise_orm = "app.settings.TORTOISE_ORM"
location = "./migrations"
src_folder = "./."
[[tool.uv.index]]
url = "https://mirrors.aliyun.com/simple"
default = true
