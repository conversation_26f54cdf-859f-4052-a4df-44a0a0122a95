"""
数据库优化迁移脚本
安全地应用索引优化到现有数据库
"""

import asyncio
import sys
import os
from pathlib import Path
from tortoise import Tortoise, connections
from tortoise.exceptions import OperationalError

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.settings.config import APP_SETTINGS
from app.log.log import log


async def check_database_connection():
    """检查数据库连接"""
    try:
        await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
        log.info("✅ 数据库连接正常")
        await Tortoise.close_connections()
        return True
    except Exception as e:
        log.error(f"❌ 数据库连接失败: {str(e)}")
        return False


async def backup_database():
    """备份数据库"""
    try:
        db_file = "app_system.sqlite3"
        if os.path.exists(db_file):
            backup_file = f"{db_file}.backup_{int(asyncio.get_event_loop().time())}"
            import shutil
            shutil.copy2(db_file, backup_file)
            log.info(f"✅ 数据库已备份到: {backup_file}")
            return backup_file
        else:
            log.warning("⚠️ 数据库文件不存在，跳过备份")
            return None
    except Exception as e:
        log.error(f"❌ 数据库备份失败: {str(e)}")
        raise


async def apply_menu_indexes(conn):
    """为Menu表添加索引"""
    menu_indexes = [
        "CREATE INDEX IF NOT EXISTS idx_menus_name ON menus(menu_name);",
        "CREATE INDEX IF NOT EXISTS idx_menus_type ON menus(menu_type);",
        "CREATE INDEX IF NOT EXISTS idx_menus_status ON menus(status_type);",
        "CREATE INDEX IF NOT EXISTS idx_menus_parent ON menus(parent_id);",
        "CREATE INDEX IF NOT EXISTS idx_menus_route_name ON menus(route_name);",
        "CREATE INDEX IF NOT EXISTS idx_menus_route_path ON menus(route_path);",
        "CREATE INDEX IF NOT EXISTS idx_menus_create_time ON menus(create_time);",
        # 复合索引
        "CREATE INDEX IF NOT EXISTS idx_menus_parent_type ON menus(parent_id, menu_type);",
        "CREATE INDEX IF NOT EXISTS idx_menus_status_type ON menus(status_type, menu_type);",
        "CREATE INDEX IF NOT EXISTS idx_menus_parent_status ON menus(parent_id, status_type);",
        "CREATE INDEX IF NOT EXISTS idx_menus_status_create_time ON menus(status_type, create_time);",
        "CREATE INDEX IF NOT EXISTS idx_menus_route_name_status ON menus(route_name, status_type);",
        "CREATE INDEX IF NOT EXISTS idx_menus_route_path_status ON menus(route_path, status_type);",
    ]
    
    for index_sql in menu_indexes:
        try:
            await conn.execute_query(index_sql)
            log.info(f"✅ Menu索引创建成功: {index_sql.split('idx_')[1].split(' ')[0]}")
        except OperationalError as e:
            if "already exists" in str(e).lower():
                log.info(f"ℹ️ Menu索引已存在: {index_sql.split('idx_')[1].split(' ')[0]}")
            else:
                log.warning(f"⚠️ Menu索引创建失败: {str(e)}")


async def apply_button_indexes(conn):
    """为Button表添加索引"""
    button_indexes = [
        "CREATE INDEX IF NOT EXISTS idx_buttons_code ON buttons(button_code);",
        "CREATE INDEX IF NOT EXISTS idx_buttons_desc ON buttons(button_desc);",
        "CREATE INDEX IF NOT EXISTS idx_buttons_status ON buttons(status_type);",
        "CREATE INDEX IF NOT EXISTS idx_buttons_create_time ON buttons(create_time);",
        # 复合索引
        "CREATE INDEX IF NOT EXISTS idx_buttons_status_code ON buttons(status_type, button_code);",
        "CREATE INDEX IF NOT EXISTS idx_buttons_status_create_time ON buttons(status_type, create_time);",
        "CREATE INDEX IF NOT EXISTS idx_buttons_desc_status ON buttons(button_desc, status_type);",
    ]
    
    for index_sql in button_indexes:
        try:
            await conn.execute_query(index_sql)
            log.info(f"✅ Button索引创建成功: {index_sql.split('idx_')[1].split(' ')[0]}")
        except OperationalError as e:
            if "already exists" in str(e).lower():
                log.info(f"ℹ️ Button索引已存在: {index_sql.split('idx_')[1].split(' ')[0]}")
            else:
                log.warning(f"⚠️ Button索引创建失败: {str(e)}")


async def apply_api_indexes(conn):
    """为Api表添加额外索引"""
    api_indexes = [
        "CREATE INDEX IF NOT EXISTS idx_apis_status ON apis(status_type);",
        "CREATE INDEX IF NOT EXISTS idx_apis_create_time ON apis(create_time);",
        "CREATE INDEX IF NOT EXISTS idx_apis_tags ON apis(tags);",
        # 复合索引
        "CREATE INDEX IF NOT EXISTS idx_apis_method_status ON apis(api_method, status_type);",
        "CREATE INDEX IF NOT EXISTS idx_apis_path_method ON apis(api_path, api_method);",
        "CREATE INDEX IF NOT EXISTS idx_apis_status_create_time ON apis(status_type, create_time);",
        "CREATE INDEX IF NOT EXISTS idx_apis_method_create_time ON apis(api_method, create_time);",
        "CREATE INDEX IF NOT EXISTS idx_apis_path_status ON apis(api_path, status_type);",
        "CREATE INDEX IF NOT EXISTS idx_apis_summary_status ON apis(summary, status_type);",
    ]
    
    for index_sql in api_indexes:
        try:
            await conn.execute_query(index_sql)
            log.info(f"✅ Api索引创建成功: {index_sql.split('idx_')[1].split(' ')[0]}")
        except OperationalError as e:
            if "already exists" in str(e).lower():
                log.info(f"ℹ️ Api索引已存在: {index_sql.split('idx_')[1].split(' ')[0]}")
            else:
                log.warning(f"⚠️ Api索引创建失败: {str(e)}")


async def optimize_database(conn):
    """优化数据库"""
    try:
        # 分析表统计信息
        await conn.execute_query("PRAGMA analysis_limit=1000;")
        await conn.execute_query("ANALYZE;")
        log.info("✅ 数据库统计信息分析完成")
        
        # 优化查询计划器
        await conn.execute_query("PRAGMA optimize;")
        log.info("✅ 查询计划器优化完成")
        
        # 清理未使用的页面
        await conn.execute_query("PRAGMA incremental_vacuum;")
        log.info("✅ 数据库清理完成")
        
    except Exception as e:
        log.warning(f"⚠️ 数据库优化过程中出现警告: {str(e)}")


async def verify_indexes(conn):
    """验证索引是否创建成功"""
    try:
        # 查询所有索引
        result = await conn.execute_query("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%';")
        indexes = [row[0] for row in result[1]]
        
        log.info(f"✅ 当前数据库中的优化索引数量: {len(indexes)}")
        for index in sorted(indexes):
            log.info(f"   - {index}")
            
        return len(indexes) > 0
    except Exception as e:
        log.error(f"❌ 验证索引失败: {str(e)}")
        return False


async def apply_optimization_migration():
    """应用数据库优化迁移"""
    log.info("🚀 开始数据库优化迁移...")
    
    # 检查数据库连接
    if not await check_database_connection():
        log.error("❌ 数据库连接失败，终止迁移")
        return False
    
    # 备份数据库
    backup_file = await backup_database()
    
    try:
        # 初始化数据库连接
        await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
        conn = connections.get("conn_system")
        
        log.info("📋 开始应用索引优化...")
        
        # 应用各表索引
        await apply_menu_indexes(conn)
        await apply_button_indexes(conn)
        await apply_api_indexes(conn)
        
        # 优化数据库
        await optimize_database(conn)
        
        # 验证索引
        if await verify_indexes(conn):
            log.info("🎉 数据库优化迁移完成！")
            log.info("📊 建议重启应用以使所有优化生效")
            return True
        else:
            log.warning("⚠️ 索引验证失败，请检查日志")
            return False
            
    except Exception as e:
        log.error(f"❌ 数据库优化迁移失败: {str(e)}")
        if backup_file:
            log.info(f"💡 如需回滚，请使用备份文件: {backup_file}")
        return False
    finally:
        await Tortoise.close_connections()


async def main():
    """主函数"""
    try:
        success = await apply_optimization_migration()
        if success:
            log.info("✅ 数据库优化完成，建议重启应用")
            sys.exit(0)
        else:
            log.error("❌ 数据库优化失败")
            sys.exit(1)
    except KeyboardInterrupt:
        log.info("⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        log.error(f"❌ 未预期的错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    print("=" * 60)
    print("StreamForge 数据库优化迁移脚本")
    print("=" * 60)
    print("此脚本将为数据库添加性能优化索引")
    print("执行前会自动备份数据库文件")
    print("=" * 60)
    
    # 确认执行
    try:
        confirm = input("是否继续执行优化？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("操作已取消")
            sys.exit(0)
    except KeyboardInterrupt:
        print("\n操作已取消")
        sys.exit(0)
    
    asyncio.run(main())
