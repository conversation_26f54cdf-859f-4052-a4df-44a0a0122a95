"""
应用ORM模型中定义的索引
通过Aerich迁移系统安全地应用索引更改
"""

import asyncio
import subprocess
import sys
from pathlib import Path
from tortoise import Tortoise

from app.settings.config import APP_SETTINGS
from app.log.log import log


async def check_database_connection():
    """检查数据库连接"""
    try:
        await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
        log.info("✅ 数据库连接正常")
        await Tortoise.close_connections()
        return True
    except Exception as e:
        log.error(f"❌ 数据库连接失败: {str(e)}")
        return False


def run_aerich_command(command: str) -> bool:
    """运行Aerich命令"""
    try:
        log.info(f"执行命令: aerich {command}")
        result = subprocess.run(
            f"aerich {command}",
            shell=True,
            capture_output=True,
            text=True,
            cwd=Path.cwd()
        )
        
        if result.returncode == 0:
            log.info(f"✅ 命令执行成功")
            if result.stdout:
                log.info(f"输出: {result.stdout}")
            return True
        else:
            log.error(f"❌ 命令执行失败")
            if result.stderr:
                log.error(f"错误: {result.stderr}")
            if result.stdout:
                log.error(f"输出: {result.stdout}")
            return False
            
    except Exception as e:
        log.error(f"❌ 执行命令时发生异常: {str(e)}")
        return False


async def backup_database():
    """备份数据库"""
    try:
        import shutil
        from datetime import datetime
        
        # 创建备份目录
        backup_dir = Path("backups")
        backup_dir.mkdir(exist_ok=True)
        
        # 生成备份文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = backup_dir / f"app_system_{timestamp}.sqlite3"
        
        # 复制数据库文件
        shutil.copy2("app_system.sqlite3", backup_file)
        
        log.info(f"✅ 数据库已备份到: {backup_file}")
        return str(backup_file)
        
    except Exception as e:
        log.error(f"❌ 数据库备份失败: {str(e)}")
        return None


async def check_aerich_status():
    """检查Aerich状态"""
    log.info("检查Aerich迁移状态...")
    
    # 检查是否已初始化
    if not Path("aerich.ini").exists():
        log.warning("⚠️ Aerich未初始化，正在初始化...")
        if not run_aerich_command("init -t app.settings.config.TORTOISE_ORM"):
            return False
    
    # 检查迁移状态
    if not run_aerich_command("status"):
        log.warning("⚠️ 无法获取迁移状态")
    
    return True


async def generate_migration():
    """生成迁移文件"""
    log.info("生成索引优化迁移文件...")
    
    # 生成迁移
    success = run_aerich_command("migrate --name index_optimization")
    
    if success:
        log.info("✅ 迁移文件生成成功")
        return True
    else:
        log.error("❌ 迁移文件生成失败")
        return False


async def apply_migration():
    """应用迁移"""
    log.info("应用索引优化迁移...")
    
    # 应用迁移
    success = run_aerich_command("upgrade")
    
    if success:
        log.info("✅ 迁移应用成功")
        return True
    else:
        log.error("❌ 迁移应用失败")
        return False


async def verify_indexes():
    """验证索引是否正确创建"""
    log.info("验证索引创建情况...")
    
    try:
        await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
        conn = Tortoise.get_connection("conn_system")
        
        # 查询所有索引
        result = await conn.execute_query(
            "SELECT name, tbl_name, sql FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'"
        )
        
        if result[1]:
            log.info(f"📊 当前数据库中的索引数量: {len(result[1])}")
            
            # 按表分组显示索引
            table_indexes = {}
            for index in result[1]:
                table_name = index['tbl_name']
                if table_name not in table_indexes:
                    table_indexes[table_name] = []
                table_indexes[table_name].append(index['name'])
            
            for table, indexes in table_indexes.items():
                log.info(f"  📋 表 {table}: {len(indexes)} 个索引")
                for idx in indexes[:5]:  # 只显示前5个
                    log.info(f"    - {idx}")
                if len(indexes) > 5:
                    log.info(f"    ... 还有 {len(indexes) - 5} 个索引")
        else:
            log.warning("⚠️ 未找到任何自定义索引")
        
        await Tortoise.close_connections()
        return True
        
    except Exception as e:
        log.error(f"❌ 验证索引失败: {str(e)}")
        return False


async def analyze_performance():
    """分析数据库性能"""
    log.info("分析数据库性能...")
    
    try:
        await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
        conn = Tortoise.get_connection("conn_system")
        
        # 获取表大小信息
        tables = [
            "strm_tasks", "strm_files", "strm_download_tasks", 
            "strm_download_logs", "strm_generation_logs", 
            "strm_upload_records", "users", "api_logs"
        ]
        
        for table in tables:
            try:
                # 获取记录数
                count_result = await conn.execute_query(f"SELECT COUNT(*) as count FROM {table}")
                count = count_result[1][0]["count"] if count_result[1] else 0
                
                log.info(f"📊 表 {table}: {count:,} 条记录")
                
            except Exception as e:
                log.warning(f"⚠️ 无法获取表 {table} 的信息: {str(e)}")
        
        # 运行ANALYZE命令优化查询计划器
        await conn.execute_query("ANALYZE")
        log.info("✅ 已运行ANALYZE命令优化查询计划器")
        
        await Tortoise.close_connections()
        return True
        
    except Exception as e:
        log.error(f"❌ 性能分析失败: {str(e)}")
        return False


async def main():
    """主函数"""
    log.info("🚀 开始应用ORM索引优化...")
    
    try:
        # 1. 检查数据库连接
        if not await check_database_connection():
            log.error("❌ 数据库连接失败，退出")
            return False
        
        # 2. 备份数据库
        backup_file = await backup_database()
        if not backup_file:
            log.error("❌ 数据库备份失败，退出")
            return False
        
        # 3. 检查Aerich状态
        if not await check_aerich_status():
            log.error("❌ Aerich状态检查失败，退出")
            return False
        
        # 4. 生成迁移文件
        if not await generate_migration():
            log.error("❌ 迁移文件生成失败，退出")
            return False
        
        # 5. 应用迁移
        if not await apply_migration():
            log.error("❌ 迁移应用失败，退出")
            return False
        
        # 6. 验证索引
        if not await verify_indexes():
            log.warning("⚠️ 索引验证失败，但迁移可能已成功")
        
        # 7. 性能分析
        if not await analyze_performance():
            log.warning("⚠️ 性能分析失败，但迁移已成功")
        
        log.info("✅ ORM索引优化完成!")
        log.info(f"📄 数据库备份文件: {backup_file}")
        
        return True
        
    except Exception as e:
        log.error(f"❌ 索引优化过程中发生异常: {str(e)}")
        return False


async def rollback_migration():
    """回滚迁移（如果需要）"""
    log.info("⚠️ 开始回滚迁移...")
    
    # 获取迁移历史
    if run_aerich_command("history"):
        # 回滚最后一次迁移
        if run_aerich_command("downgrade"):
            log.info("✅ 迁移回滚成功")
            return True
    
    log.error("❌ 迁移回滚失败")
    return False


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--rollback":
        asyncio.run(rollback_migration())
    else:
        success = asyncio.run(main())
        if not success:
            sys.exit(1)
