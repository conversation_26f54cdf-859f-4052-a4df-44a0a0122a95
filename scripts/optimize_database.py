"""
数据库优化脚本
安全地添加索引和优化数据库性能
"""

import asyncio
from tortoise import Tortoise, connections
from app.settings.config import APP_SETTINGS
from app.log.log import log


async def create_performance_indexes():
    """创建性能优化索引"""
    
    # 初始化数据库连接
    await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
    conn = connections.get("conn_system")
    
    # 定义需要创建的索引
    indexes = [
        # User表索引
        "CREATE INDEX IF NOT EXISTS idx_users_email_status ON users(user_email, status_type);",
        "CREATE INDEX IF NOT EXISTS idx_users_phone_status ON users(user_phone, status_type);",
        "CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login);",
        "CREATE INDEX IF NOT EXISTS idx_users_status_create_time ON users(status_type, create_time);",
        
        # MediaServer表索引
        "CREATE INDEX IF NOT EXISTS idx_media_servers_type ON strm_media_servers(server_type);",
        "CREATE INDEX IF NOT EXISTS idx_media_servers_status ON strm_media_servers(status);",
        "CREATE INDEX IF NOT EXISTS idx_media_servers_type_status ON strm_media_servers(server_type, status);",
        "CREATE INDEX IF NOT EXISTS idx_media_servers_status_create_time ON strm_media_servers(status, create_time);",
        
        # SystemSettings表索引
        "CREATE INDEX IF NOT EXISTS idx_system_settings_version ON strm_system_settings(settings_version);",
        "CREATE INDEX IF NOT EXISTS idx_system_settings_update_time ON strm_system_settings(update_time);",
        
        # StrmFile表索引
        "CREATE INDEX IF NOT EXISTS idx_strm_files_task_type ON strm_files(task_id, file_type);",
        "CREATE INDEX IF NOT EXISTS idx_strm_files_task_success ON strm_files(task_id, is_success);",
        "CREATE INDEX IF NOT EXISTS idx_strm_files_task_create_time ON strm_files(task_id, create_time);",
        "CREATE INDEX IF NOT EXISTS idx_strm_files_task_type_success ON strm_files(task_id, file_type, is_success);",
        
        # API日志表索引
        "CREATE INDEX IF NOT EXISTS idx_api_logs_user_id ON api_logs(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_api_logs_path_method ON api_logs(request_path, request_method);",
        "CREATE INDEX IF NOT EXISTS idx_api_logs_response_code_time ON api_logs(response_code, create_time);",
        
        # STRM日志表索引
        "CREATE INDEX IF NOT EXISTS idx_strm_logs_task_level ON strm_logs(task_id, log_level);",
        "CREATE INDEX IF NOT EXISTS idx_strm_logs_task_create_time ON strm_logs(task_id, create_time);",
        
        # 下载日志表索引
        "CREATE INDEX IF NOT EXISTS idx_download_logs_task_level ON strm_download_logs(task_id, log_level);",
        "CREATE INDEX IF NOT EXISTS idx_download_logs_task_create_time ON strm_download_logs(task_id, create_time);",
    ]
    
    success_count = 0
    error_count = 0
    
    log.info("🚀 开始创建性能优化索引...")
    
    for index_sql in indexes:
        try:
            await conn.execute_query(index_sql)
            success_count += 1
            index_name = index_sql.split("IF NOT EXISTS")[1].split("ON")[0].strip()
            log.info(f"✅ 索引创建成功: {index_name}")
        except Exception as e:
            error_count += 1
            log.error(f"❌ 索引创建失败: {index_sql[:50]}... - 错误: {str(e)}")
    
    log.info(f"📊 索引创建完成: 成功 {success_count} 个, 失败 {error_count} 个")
    
    # 分析表以更新统计信息
    tables = [
        "users", "strm_tasks", "strm_files", "strm_media_servers",
        "strm_system_settings", "api_logs", "strm_logs", "strm_download_logs"
    ]
    
    log.info("📈 开始分析表统计信息...")
    for table in tables:
        try:
            await conn.execute_query(f"ANALYZE {table};")
            log.info(f"✅ 表分析完成: {table}")
        except Exception as e:
            log.error(f"❌ 表分析失败: {table} - 错误: {str(e)}")
    
    # 执行数据库优化
    log.info("⚡ 执行数据库优化...")
    try:
        await conn.execute_query("PRAGMA optimize;")
        log.info("✅ 数据库优化完成")
    except Exception as e:
        log.error(f"❌ 数据库优化失败: {str(e)}")
    
    await Tortoise.close_connections()
    log.info("🎉 数据库性能优化完成!")
    
    return {"success": success_count, "error": error_count}


async def check_database_status():
    """检查数据库状态"""
    
    await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
    conn = connections.get("conn_system")
    
    # 检查索引信息
    index_query = """
    SELECT 
        m.name as table_name,
        COUNT(il.name) as index_count
    FROM sqlite_master m
    LEFT JOIN pragma_index_list(m.name) il ON 1=1
    WHERE m.type = 'table' 
    AND m.name NOT LIKE 'sqlite_%'
    GROUP BY m.name
    ORDER BY m.name
    """
    
    try:
        result = await conn.execute_query(index_query)
        log.info("📊 数据库表索引统计:")
        for row in result[1] or []:
            log.info(f"  {row['table_name']}: {row['index_count']} 个索引")
    except Exception as e:
        log.error(f"❌ 获取索引统计失败: {str(e)}")
    
    # 检查数据库大小
    try:
        size_result = await conn.execute_query("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size();")
        if size_result[1]:
            size_mb = size_result[1][0]['size'] / (1024 * 1024)
            log.info(f"💾 数据库大小: {size_mb:.2f} MB")
    except Exception as e:
        log.error(f"❌ 获取数据库大小失败: {str(e)}")
    
    await Tortoise.close_connections()


async def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "check":
        await check_database_status()
    else:
        await create_performance_indexes()


if __name__ == "__main__":
    asyncio.run(main())
