"""
性能测试脚本
测试数据库优化前后的性能差异
"""

import asyncio
import time
import statistics
from typing import List, Dict, Any

from tortoise import Tortoise
from app.settings.config import APP_SETTINGS
from app.models.strm.upload import UploadRecord
from app.models.system import User
from app.controllers.strm.file_content_optimizer import FileContentOptimizer
from app.core.enhanced_cache_manager import EnhancedCacheManager
from app.log.log import log


class PerformanceTest:
    """性能测试类"""
    
    def __init__(self):
        self.test_results = {}
    
    async def run_all_tests(self):
        """运行所有性能测试"""
        log.info("🚀 开始性能测试...")
        
        # 初始化数据库
        await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
        
        try:
            # 获取测试用户
            test_user = await self._get_or_create_test_user()
            
            # 测试1: 列表查询性能
            await self._test_list_query_performance(test_user)
            
            # 测试2: 文件内容查询性能
            await self._test_file_content_performance(test_user)
            
            # 测试3: 缓存性能
            await self._test_cache_performance(test_user)
            
            # 测试4: 并发性能
            await self._test_concurrent_performance(test_user)
            
            # 生成测试报告
            self._generate_report()
            
        finally:
            await Tortoise.close_connections()
    
    async def _get_or_create_test_user(self) -> User:
        """获取或创建测试用户"""
        user = await User.get_or_none(user_name="test_performance")
        if not user:
            user = await User.create(
                user_name="test_performance",
                password="test_password",
                user_email="<EMAIL>",
                nick_name="性能测试用户"
            )
        return user
    
    async def _test_list_query_performance(self, user: User):
        """测试列表查询性能"""
        log.info("📊 测试列表查询性能...")
        
        # 测试原始查询（包含文件内容）
        times_original = []
        for i in range(5):
            start_time = time.time()
            records = await UploadRecord.filter(uploader=user).limit(10).all()
            end_time = time.time()
            times_original.append(end_time - start_time)
        
        # 测试优化查询（排除文件内容）
        times_optimized = []
        for i in range(5):
            start_time = time.time()
            result = await FileContentOptimizer.get_upload_list_optimized(
                user, page=1, page_size=10
            )
            end_time = time.time()
            times_optimized.append(end_time - start_time)
        
        self.test_results["list_query"] = {
            "original_avg": statistics.mean(times_original),
            "optimized_avg": statistics.mean(times_optimized),
            "improvement": (statistics.mean(times_original) - statistics.mean(times_optimized)) / statistics.mean(times_original) * 100
        }
        
        log.info(f"列表查询 - 原始: {statistics.mean(times_original):.3f}s, 优化: {statistics.mean(times_optimized):.3f}s")
    
    async def _test_file_content_performance(self, user: User):
        """测试文件内容查询性能"""
        log.info("📁 测试文件内容查询性能...")
        
        # 获取测试记录
        records = await UploadRecord.filter(uploader=user).limit(5).all()
        if not records:
            log.warning("没有找到测试记录，跳过文件内容性能测试")
            return
        
        record_ids = [record.id for record in records]
        
        # 测试直接数据库查询
        times_direct = []
        for record_id in record_ids:
            start_time = time.time()
            record = await UploadRecord.get(id=record_id)
            content = record.file_content
            end_time = time.time()
            times_direct.append(end_time - start_time)
        
        # 测试缓存查询
        times_cached = []
        for record_id in record_ids:
            start_time = time.time()
            content = await FileContentOptimizer.get_file_content_cached(record_id)
            end_time = time.time()
            times_cached.append(end_time - start_time)
        
        # 测试缓存命中
        times_cache_hit = []
        for record_id in record_ids:
            start_time = time.time()
            content = await FileContentOptimizer.get_file_content_cached(record_id)
            end_time = time.time()
            times_cache_hit.append(end_time - start_time)
        
        self.test_results["file_content"] = {
            "direct_avg": statistics.mean(times_direct),
            "cached_avg": statistics.mean(times_cached),
            "cache_hit_avg": statistics.mean(times_cache_hit),
            "cache_improvement": (statistics.mean(times_direct) - statistics.mean(times_cache_hit)) / statistics.mean(times_direct) * 100
        }
        
        log.info(f"文件内容 - 直接: {statistics.mean(times_direct):.3f}s, 缓存: {statistics.mean(times_cache_hit):.3f}s")
    
    async def _test_cache_performance(self, user: User):
        """测试缓存性能"""
        log.info("🗄️ 测试缓存性能...")
        
        # 获取缓存统计
        stats_before = await EnhancedCacheManager.get_cache_stats()
        
        # 执行一些缓存操作
        for i in range(10):
            await FileContentOptimizer.get_upload_list_optimized(user, page=1, page_size=5)
        
        stats_after = await EnhancedCacheManager.get_cache_stats()
        
        self.test_results["cache"] = {
            "items_before": stats_before["total_items"],
            "items_after": stats_after["total_items"],
            "memory_usage_mb": stats_after["memory_usage_mb"],
            "cache_types": list(stats_after["by_type"].keys())
        }
        
        log.info(f"缓存项目: {stats_after['total_items']}, 内存使用: {stats_after['memory_usage_mb']:.2f}MB")
    
    async def _test_concurrent_performance(self, user: User):
        """测试并发性能"""
        log.info("🔄 测试并发性能...")
        
        async def single_request():
            return await FileContentOptimizer.get_upload_list_optimized(user, page=1, page_size=5)
        
        # 测试并发请求
        start_time = time.time()
        tasks = [single_request() for _ in range(10)]
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        total_time = end_time - start_time
        avg_time_per_request = total_time / len(tasks)
        
        self.test_results["concurrent"] = {
            "total_requests": len(tasks),
            "total_time": total_time,
            "avg_time_per_request": avg_time_per_request,
            "requests_per_second": len(tasks) / total_time
        }
        
        log.info(f"并发测试 - 10个请求总时间: {total_time:.3f}s, 平均: {avg_time_per_request:.3f}s")
    
    def _generate_report(self):
        """生成测试报告"""
        log.info("📋 生成性能测试报告...")
        
        report = "\n" + "="*60 + "\n"
        report += "           性能测试报告\n"
        report += "="*60 + "\n"
        
        # 列表查询性能
        if "list_query" in self.test_results:
            data = self.test_results["list_query"]
            report += f"\n📊 列表查询性能:\n"
            report += f"   原始查询平均时间: {data['original_avg']:.3f}s\n"
            report += f"   优化查询平均时间: {data['optimized_avg']:.3f}s\n"
            report += f"   性能提升: {data['improvement']:.1f}%\n"
        
        # 文件内容性能
        if "file_content" in self.test_results:
            data = self.test_results["file_content"]
            report += f"\n📁 文件内容查询性能:\n"
            report += f"   直接查询平均时间: {data['direct_avg']:.3f}s\n"
            report += f"   缓存查询平均时间: {data['cached_avg']:.3f}s\n"
            report += f"   缓存命中平均时间: {data['cache_hit_avg']:.3f}s\n"
            report += f"   缓存性能提升: {data['cache_improvement']:.1f}%\n"
        
        # 缓存性能
        if "cache" in self.test_results:
            data = self.test_results["cache"]
            report += f"\n🗄️ 缓存性能:\n"
            report += f"   缓存项目数量: {data['items_after']}\n"
            report += f"   内存使用: {data['memory_usage_mb']:.2f}MB\n"
            report += f"   缓存类型: {', '.join(data['cache_types'])}\n"
        
        # 并发性能
        if "concurrent" in self.test_results:
            data = self.test_results["concurrent"]
            report += f"\n🔄 并发性能:\n"
            report += f"   并发请求数: {data['total_requests']}\n"
            report += f"   总执行时间: {data['total_time']:.3f}s\n"
            report += f"   平均响应时间: {data['avg_time_per_request']:.3f}s\n"
            report += f"   每秒请求数: {data['requests_per_second']:.1f} req/s\n"
        
        report += "\n" + "="*60 + "\n"
        
        # 输出报告
        print(report)
        
        # 保存报告到文件
        with open("performance_test_report.txt", "w", encoding="utf-8") as f:
            f.write(report)
        
        log.info("📄 测试报告已保存到 performance_test_report.txt")


async def run_quick_test():
    """运行快速性能测试"""
    log.info("⚡ 运行快速性能测试...")
    
    await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
    
    try:
        # 测试数据库连接
        start_time = time.time()
        count = await UploadRecord.all().count()
        end_time = time.time()
        
        log.info(f"数据库连接测试: {end_time - start_time:.3f}s, 记录数: {count}")
        
        # 测试缓存
        cache_stats = await EnhancedCacheManager.get_cache_stats()
        log.info(f"当前缓存状态: {cache_stats['total_items']} 项, {cache_stats['memory_usage_mb']:.2f}MB")
        
    finally:
        await Tortoise.close_connections()


async def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        await run_quick_test()
    else:
        test = PerformanceTest()
        await test.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
