"""
验证数据库索引脚本
检查ORM模型中定义的索引是否正确创建
"""

import asyncio
from tortoise import Tortoise
from app.settings.config import APP_SETTINGS
from app.log.log import log


# 定义期望的索引（基于ORM模型定义）
EXPECTED_INDEXES = {
    "strm_tasks": [
        "created_by_id", "status", "create_time", "name", "server_id", 
        "download_server_id", "start_time", "end_time"
    ],
    "strm_files": [
        "task_id", "file_type", "is_success", "create_time", "file_size"
    ],
    "strm_download_logs": [
        "task_id", "file_type", "is_success", "log_level", "create_time",
        "download_time", "download_speed", "file_size"
    ],
    "strm_generation_logs": [
        "task_id", "file_type", "is_success", "log_level", "create_time",
        "generation_time"
    ],
    "strm_upload_records": [
        "uploader_id", "status", "create_time", "parse_time", "filename"
    ],
    "api_logs": [
        "create_time", "process_time", "x_request_id", "request_path",
        "response_code", "user_id"
    ]
}


async def get_database_indexes():
    """获取数据库中的所有索引"""
    try:
        await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
        conn = Tortoise.get_connection("conn_system")
        
        # 查询所有索引
        result = await conn.execute_query(
            """
            SELECT name, tbl_name, sql 
            FROM sqlite_master 
            WHERE type='index' 
            AND name NOT LIKE 'sqlite_%'
            ORDER BY tbl_name, name
            """
        )
        
        indexes_by_table = {}
        if result[1]:
            for index in result[1]:
                table_name = index['tbl_name']
                index_name = index['name']
                index_sql = index['sql'] or ''
                
                if table_name not in indexes_by_table:
                    indexes_by_table[table_name] = []
                
                indexes_by_table[table_name].append({
                    'name': index_name,
                    'sql': index_sql
                })
        
        await Tortoise.close_connections()
        return indexes_by_table
        
    except Exception as e:
        log.error(f"获取数据库索引失败: {str(e)}")
        return {}


def analyze_index_coverage(db_indexes):
    """分析索引覆盖情况"""
    log.info("📊 分析索引覆盖情况...")
    
    coverage_report = {}
    
    for table, expected_fields in EXPECTED_INDEXES.items():
        coverage_report[table] = {
            'expected_count': len(expected_fields),
            'actual_count': len(db_indexes.get(table, [])),
            'covered_fields': [],
            'missing_fields': [],
            'extra_indexes': []
        }
        
        actual_indexes = db_indexes.get(table, [])
        
        # 分析每个期望的字段是否有索引
        for field in expected_fields:
            has_index = False
            for index in actual_indexes:
                # 简单检查索引名称或SQL中是否包含字段名
                if field in index['name'] or field in index['sql']:
                    has_index = True
                    break
            
            if has_index:
                coverage_report[table]['covered_fields'].append(field)
            else:
                coverage_report[table]['missing_fields'].append(field)
        
        # 检查额外的索引
        for index in actual_indexes:
            is_expected = False
            for field in expected_fields:
                if field in index['name'] or field in index['sql']:
                    is_expected = True
                    break
            
            if not is_expected:
                coverage_report[table]['extra_indexes'].append(index['name'])
    
    return coverage_report


def print_coverage_report(coverage_report):
    """打印覆盖率报告"""
    log.info("\n" + "="*60)
    log.info("           索引覆盖率报告")
    log.info("="*60)
    
    total_expected = 0
    total_covered = 0
    
    for table, report in coverage_report.items():
        expected = report['expected_count']
        covered = len(report['covered_fields'])
        coverage_pct = (covered / expected * 100) if expected > 0 else 0
        
        total_expected += expected
        total_covered += covered
        
        log.info(f"\n📋 表: {table}")
        log.info(f"   期望索引: {expected} 个")
        log.info(f"   已覆盖: {covered} 个")
        log.info(f"   覆盖率: {coverage_pct:.1f}%")
        
        if report['missing_fields']:
            log.warning(f"   ⚠️ 缺失字段索引: {', '.join(report['missing_fields'])}")
        
        if report['extra_indexes']:
            log.info(f"   ➕ 额外索引: {', '.join(report['extra_indexes'])}")
        
        if coverage_pct == 100:
            log.info("   ✅ 索引覆盖完整")
    
    # 总体覆盖率
    overall_coverage = (total_covered / total_expected * 100) if total_expected > 0 else 0
    log.info(f"\n📊 总体覆盖率: {overall_coverage:.1f}% ({total_covered}/{total_expected})")
    
    if overall_coverage >= 90:
        log.info("✅ 索引覆盖率良好")
    elif overall_coverage >= 70:
        log.warning("⚠️ 索引覆盖率一般，建议优化")
    else:
        log.error("❌ 索引覆盖率较低，需要优化")


async def check_index_performance():
    """检查索引性能"""
    log.info("🔍 检查索引性能...")
    
    try:
        await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
        conn = Tortoise.get_connection("conn_system")
        
        # 运行ANALYZE命令
        await conn.execute_query("ANALYZE")
        log.info("✅ 已运行ANALYZE命令更新统计信息")
        
        # 检查一些常见查询的执行计划
        test_queries = [
            "SELECT * FROM strm_tasks WHERE created_by_id = 1 ORDER BY create_time DESC LIMIT 10",
            "SELECT * FROM strm_files WHERE task_id = 1 AND is_success = 1",
            "SELECT * FROM strm_upload_records WHERE uploader_id = 1 AND status = 'parsed'",
            "SELECT COUNT(*) FROM strm_download_logs WHERE task_id = 1 AND log_level = 'INFO'"
        ]
        
        for query in test_queries:
            try:
                # 获取查询执行计划
                explain_result = await conn.execute_query(f"EXPLAIN QUERY PLAN {query}")
                
                if explain_result[1]:
                    plan = explain_result[1][0]
                    detail = plan.get('detail', '')
                    
                    # 检查是否使用了索引
                    if 'USING INDEX' in detail:
                        log.info(f"✅ 查询使用索引: {detail}")
                    else:
                        log.warning(f"⚠️ 查询未使用索引: {detail}")
                
            except Exception as e:
                log.warning(f"⚠️ 无法分析查询: {str(e)}")
        
        await Tortoise.close_connections()
        
    except Exception as e:
        log.error(f"检查索引性能失败: {str(e)}")


async def main():
    """主函数"""
    log.info("🔍 开始验证数据库索引...")
    
    try:
        # 获取数据库中的索引
        db_indexes = await get_database_indexes()
        
        if not db_indexes:
            log.error("❌ 无法获取数据库索引信息")
            return False
        
        log.info(f"📊 数据库中共有 {sum(len(indexes) for indexes in db_indexes.values())} 个索引")
        
        # 分析索引覆盖情况
        coverage_report = analyze_index_coverage(db_indexes)
        
        # 打印报告
        print_coverage_report(coverage_report)
        
        # 检查索引性能
        await check_index_performance()
        
        log.info("✅ 索引验证完成")
        return True
        
    except Exception as e:
        log.error(f"❌ 索引验证失败: {str(e)}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    if not success:
        exit(1)
