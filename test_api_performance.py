#!/usr/bin/env python3
"""
API性能测试脚本
测试系统中所有主要接口的响应时间
"""

import asyncio
import aiohttp
import time
import json
from typing import Dict, Any


async def test_api_performance():
    """测试API性能"""
    base_url = "http://localhost:9999/api/v1"

    # 测试数据 - 覆盖所有主要接口
    test_cases = [
        # 权限认证模块
        {
            "name": "用户信息接口",
            "url": f"{base_url}/auth/user-info",
            "method": "GET",
            "data": None,
            "headers": {"Authorization": "Bearer test_token"}
        },

        # 路由管理模块
        {
            "name": "常量路由接口（优化后）",
            "url": f"{base_url}/route/constant-routes",
            "method": "GET",
            "data": None
        },
        {
            "name": "用户路由接口",
            "url": f"{base_url}/route/user-routes",
            "method": "GET",
            "data": None,
            "headers": {"Authorization": "Bearer test_token"}
        },

        # 系统管理模块 - 用户管理
        {
            "name": "用户列表接口",
            "url": f"{base_url}/system-manage/users/all/",
            "method": "POST",
            "data": {
                "current": 1,
                "size": 10
            }
        },

        # 系统管理模块 - 日志管理
        {
            "name": "日志列表接口",
            "url": f"{base_url}/system-manage/logs/all/",
            "method": "POST",
            "data": {
                "current": 1,
                "size": 10,
                "log_type": "ApiLog"
            }
        },

        # 系统管理模块 - API管理
        {
            "name": "API列表接口",
            "url": f"{base_url}/system-manage/apis/all/",
            "method": "POST",
            "data": {
                "current": 1,
                "size": 10
            }
        },
        {
            "name": "API树接口",
            "url": f"{base_url}/system-manage/apis/tree/",
            "method": "GET",
            "data": None
        },
        {
            "name": "API标签接口",
            "url": f"{base_url}/system-manage/apis/tags/all/",
            "method": "POST",
            "data": {}
        },

        # 系统管理模块 - 菜单管理
        {
            "name": "菜单树接口",
            "url": f"{base_url}/system-manage/menus/tree/",
            "method": "GET",
            "data": None
        },
        {
            "name": "菜单按钮树接口",
            "url": f"{base_url}/system-manage/menus/buttons/tree/",
            "method": "GET",
            "data": None
        },

        # 系统管理模块 - 角色管理
        {
            "name": "角色列表接口",
            "url": f"{base_url}/system-manage/roles/all/",
            "method": "POST",
            "data": {
                "current": 1,
                "size": 10
            }
        },

        # 系统管理模块 - 系统设置
        {
            "name": "系统设置获取接口",
            "url": f"{base_url}/system-manage/settings",
            "method": "GET",
            "data": None
        },

        # STRM文件管理模块
        {
            "name": "上传历史接口",
            "url": f"{base_url}/strm/upload/history",
            "method": "GET",
            "data": None
        },
        {
            "name": "媒体服务器列表接口",
            "url": f"{base_url}/strm/media-servers",
            "method": "GET",
            "data": None
        },
        {
            "name": "下载服务器列表接口",
            "url": f"{base_url}/strm/download-servers",
            "method": "GET",
            "data": None
        },
        {
            "name": "任务列表接口",
            "url": f"{base_url}/strm/tasks",
            "method": "GET",
            "data": None
        }
    ]

    async with aiohttp.ClientSession() as session:
        print("🚀 开始API性能测试...")
        print("=" * 80)
        print(f"📋 总共测试接口数量: {len(test_cases)}")
        print("=" * 80)

        total_success = 0
        total_failed = 0
        all_response_times = []

        for idx, test_case in enumerate(test_cases, 1):
            print(f"\n📊 [{idx}/{len(test_cases)}] 测试接口: {test_case['name']}")
            print(f"🔗 URL: {test_case['url']}")
            print(f"📝 方法: {test_case['method']}")

            # 准备请求头
            headers = test_case.get('headers', {})
            if test_case['data'] is not None and 'Content-Type' not in headers:
                headers['Content-Type'] = 'application/json'

            # 预热请求
            try:
                if test_case['method'] == 'GET':
                    async with session.get(test_case['url'], headers=headers) as response:
                        await response.text()
                else:
                    async with session.request(
                        test_case['method'],
                        test_case['url'],
                        json=test_case['data'],
                        headers=headers
                    ) as response:
                        await response.text()
                print("🔥 预热请求完成")
            except Exception as e:
                print(f"❌ 预热请求失败: {e}")
                print(f"⚠️  跳过此接口测试")
                total_failed += 1
                continue

            # 性能测试 - 多次请求取平均值
            response_times = []
            success_count = 0
            test_rounds = 5  # 减少测试轮数以加快测试速度

            for i in range(test_rounds):
                try:
                    start_time = time.time()
                    if test_case['method'] == 'GET':
                        async with session.get(test_case['url'], headers=headers) as response:
                            await response.text()
                            end_time = time.time()
                    else:
                        async with session.request(
                            test_case['method'],
                            test_case['url'],
                            json=test_case['data'],
                            headers=headers
                        ) as response:
                            await response.text()
                            end_time = time.time()

                    if response.status == 200:
                        response_time = (end_time - start_time) * 1000  # 转换为毫秒
                        response_times.append(response_time)
                        success_count += 1
                        print(f"  ✅ 第{i+1}次请求: {response_time:.2f}ms")
                    else:
                        print(f"  ❌ 第{i+1}次请求失败: HTTP {response.status}")

                except Exception as e:
                    print(f"  ❌ 第{i+1}次请求异常: {e}")

                # 请求间隔
                await asyncio.sleep(0.05)

            # 统计结果
            if response_times:
                avg_time = sum(response_times) / len(response_times)
                min_time = min(response_times)
                max_time = max(response_times)
                all_response_times.extend(response_times)
                total_success += 1

                print(f"\n📈 性能统计结果:")
                print(f"  ✅ 成功请求: {success_count}/{test_rounds}")
                print(f"  ⏱️  平均响应时间: {avg_time:.2f}ms")
                print(f"  🚀 最快响应时间: {min_time:.2f}ms")
                print(f"  🐌 最慢响应时间: {max_time:.2f}ms")

                # 性能评估
                if avg_time < 30:
                    print(f"  🎉 性能优秀! (目标: <30ms)")
                elif avg_time < 50:
                    print(f"  👍 性能良好! (目标: <30ms)")
                elif avg_time < 100:
                    print(f"  ⚠️  性能一般 (目标: <30ms)")
                else:
                    print(f"  ❌ 性能需要优化 (目标: <30ms)")
            else:
                print(f"  ❌ 所有请求都失败了")
                total_failed += 1

            print("-" * 80)

        # 总体统计
        print(f"\n🏁 API性能测试完成!")
        print("=" * 80)
        print(f"📊 总体测试结果:")
        print(f"  📋 测试接口总数: {len(test_cases)}")
        print(f"  ✅ 成功接口数量: {total_success}")
        print(f"  ❌ 失败接口数量: {total_failed}")
        print(f"  📈 成功率: {(total_success / len(test_cases) * 100):.1f}%")

        if all_response_times:
            overall_avg = sum(all_response_times) / len(all_response_times)
            overall_min = min(all_response_times)
            overall_max = max(all_response_times)

            print(f"\n🎯 整体性能指标:")
            print(f"  ⏱️  总体平均响应时间: {overall_avg:.2f}ms")
            print(f"  🚀 全局最快响应时间: {overall_min:.2f}ms")
            print(f"  🐌 全局最慢响应时间: {overall_max:.2f}ms")

            # 整体性能评估
            if overall_avg < 30:
                print(f"  🎉 整体性能优秀!")
            elif overall_avg < 50:
                print(f"  👍 整体性能良好!")
            elif overall_avg < 100:
                print(f"  ⚠️  整体性能一般")
            else:
                print(f"  ❌ 整体性能需要优化")

        print("=" * 80)


if __name__ == "__main__":
    asyncio.run(test_api_performance())
