#!/usr/bin/env python3
"""
API性能测试脚本
测试优化后的系统管理接口响应时间
"""

import asyncio
import aiohttp
import time
import json
from typing import Dict, Any


async def test_api_performance():
    """测试API性能"""
    base_url = "http://localhost:9999/api/v1/system-manage"
    
    # 测试数据
    test_cases = [
        {
            "name": "用户列表接口",
            "url": f"{base_url}/users/all/",
            "method": "POST",
            "data": {
                "current": 1,
                "size": 10
            }
        },
        {
            "name": "日志列表接口", 
            "url": f"{base_url}/logs/all/",
            "method": "POST",
            "data": {
                "current": 1,
                "size": 10,
                "log_type": "ApiLog"
            }
        }
    ]
    
    async with aiohttp.ClientSession() as session:
        print("🚀 开始API性能测试...")
        print("=" * 60)
        
        for test_case in test_cases:
            print(f"\n📊 测试接口: {test_case['name']}")
            print(f"🔗 URL: {test_case['url']}")
            
            # 预热请求
            try:
                async with session.request(
                    test_case['method'],
                    test_case['url'],
                    json=test_case['data'],
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    await response.text()
                print("🔥 预热请求完成")
            except Exception as e:
                print(f"❌ 预热请求失败: {e}")
                continue
            
            # 性能测试 - 多次请求取平均值
            response_times = []
            success_count = 0
            test_rounds = 10
            
            for i in range(test_rounds):
                try:
                    start_time = time.time()
                    async with session.request(
                        test_case['method'],
                        test_case['url'],
                        json=test_case['data'],
                        headers={'Content-Type': 'application/json'}
                    ) as response:
                        await response.text()
                        end_time = time.time()
                        
                        if response.status == 200:
                            response_time = (end_time - start_time) * 1000  # 转换为毫秒
                            response_times.append(response_time)
                            success_count += 1
                            print(f"  ✅ 第{i+1}次请求: {response_time:.2f}ms")
                        else:
                            print(f"  ❌ 第{i+1}次请求失败: HTTP {response.status}")
                            
                except Exception as e:
                    print(f"  ❌ 第{i+1}次请求异常: {e}")
                
                # 请求间隔
                await asyncio.sleep(0.1)
            
            # 统计结果
            if response_times:
                avg_time = sum(response_times) / len(response_times)
                min_time = min(response_times)
                max_time = max(response_times)
                
                print(f"\n📈 性能统计结果:")
                print(f"  ✅ 成功请求: {success_count}/{test_rounds}")
                print(f"  ⏱️  平均响应时间: {avg_time:.2f}ms")
                print(f"  🚀 最快响应时间: {min_time:.2f}ms")
                print(f"  🐌 最慢响应时间: {max_time:.2f}ms")
                
                # 性能评估
                if avg_time < 30:
                    print(f"  🎉 性能优秀! (目标: <30ms)")
                elif avg_time < 50:
                    print(f"  👍 性能良好! (目标: <30ms)")
                elif avg_time < 70:
                    print(f"  ⚠️  性能一般 (目标: <30ms)")
                else:
                    print(f"  ❌ 性能需要优化 (目标: <30ms)")
            else:
                print(f"  ❌ 所有请求都失败了")
            
            print("-" * 60)
        
        print("\n🏁 API性能测试完成!")


if __name__ == "__main__":
    asyncio.run(test_api_performance())
