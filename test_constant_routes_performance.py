#!/usr/bin/env python3
"""
常量路由接口性能专项测试
测试优化前后的性能差异
"""

import asyncio
import aiohttp
import time
import statistics
from typing import List


async def test_constant_routes_performance():
    """专门测试常量路由接口性能"""
    url = "http://localhost:9999/api/v1/route/constant-routes"
    
    print("🚀 开始常量路由接口性能专项测试...")
    print("=" * 80)
    print(f"🔗 测试URL: {url}")
    print(f"📝 测试方法: GET")
    print("=" * 80)
    
    async with aiohttp.ClientSession() as session:
        # 预热请求（清除缓存影响）
        print("🔥 执行预热请求...")
        try:
            async with session.get(url) as response:
                await response.text()
            print("✅ 预热请求完成")
        except Exception as e:
            print(f"❌ 预热请求失败: {e}")
            return
        
        # 性能测试 - 更多轮次获得更准确的数据
        response_times: List[float] = []
        success_count = 0
        test_rounds = 20  # 增加测试轮次
        
        print(f"\n📊 开始性能测试 ({test_rounds} 轮)...")
        print("-" * 80)
        
        for i in range(test_rounds):
            try:
                start_time = time.perf_counter()  # 使用更精确的计时器
                async with session.get(url) as response:
                    await response.text()
                    end_time = time.perf_counter()
                
                if response.status == 200:
                    response_time = (end_time - start_time) * 1000  # 转换为毫秒
                    response_times.append(response_time)
                    success_count += 1
                    
                    # 显示进度
                    if i % 5 == 0 or i == test_rounds - 1:
                        print(f"  ✅ 第{i+1:2d}次请求: {response_time:6.2f}ms")
                else:
                    print(f"  ❌ 第{i+1}次请求失败: HTTP {response.status}")
                    
            except Exception as e:
                print(f"  ❌ 第{i+1}次请求异常: {e}")
            
            # 短暂间隔，避免过于密集的请求
            await asyncio.sleep(0.02)
        
        # 详细统计分析
        if response_times:
            print("\n" + "=" * 80)
            print("📈 详细性能统计结果:")
            print("=" * 80)
            
            # 基本统计
            avg_time = statistics.mean(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            median_time = statistics.median(response_times)
            
            print(f"  ✅ 成功请求数量: {success_count}/{test_rounds} ({success_count/test_rounds*100:.1f}%)")
            print(f"  ⏱️  平均响应时间: {avg_time:.2f}ms")
            print(f"  📊 中位数响应时间: {median_time:.2f}ms")
            print(f"  🚀 最快响应时间: {min_time:.2f}ms")
            print(f"  🐌 最慢响应时间: {max_time:.2f}ms")
            
            # 高级统计
            if len(response_times) > 1:
                std_dev = statistics.stdev(response_times)
                print(f"  📏 标准差: {std_dev:.2f}ms")
                
                # 百分位数
                sorted_times = sorted(response_times)
                p95 = sorted_times[int(len(sorted_times) * 0.95)]
                p99 = sorted_times[int(len(sorted_times) * 0.99)]
                print(f"  📊 95%百分位: {p95:.2f}ms")
                print(f"  📊 99%百分位: {p99:.2f}ms")
            
            print("\n" + "-" * 80)
            
            # 性能评估
            print("🎯 性能评估:")
            if avg_time < 5:
                print("  🎉 性能卓越! (< 5ms)")
            elif avg_time < 10:
                print("  🌟 性能优秀! (< 10ms)")
            elif avg_time < 20:
                print("  👍 性能良好! (< 20ms)")
            elif avg_time < 30:
                print("  ⚠️  性能一般 (< 30ms)")
            else:
                print("  ❌ 性能需要优化 (>= 30ms)")
            
            # 稳定性评估
            if len(response_times) > 1:
                cv = (std_dev / avg_time) * 100  # 变异系数
                print(f"🔄 稳定性评估 (变异系数: {cv:.1f}%):")
                if cv < 10:
                    print("  🎯 响应时间非常稳定")
                elif cv < 20:
                    print("  ✅ 响应时间稳定")
                elif cv < 30:
                    print("  ⚠️  响应时间波动较大")
                else:
                    print("  ❌ 响应时间不稳定")
            
            # 优化建议
            print("\n💡 优化建议:")
            if avg_time > 10:
                print("  • 考虑增加缓存时间")
                print("  • 检查数据库索引是否生效")
                print("  • 优化查询语句")
            if max_time > avg_time * 3:
                print("  • 存在异常慢的请求，建议检查系统负载")
            if cv > 20:
                print("  • 响应时间不稳定，建议检查系统资源")
                
        else:
            print("❌ 所有请求都失败了")
        
        print("\n" + "=" * 80)
        print("🏁 常量路由接口性能测试完成!")
        print("=" * 80)


if __name__ == "__main__":
    asyncio.run(test_constant_routes_performance())
